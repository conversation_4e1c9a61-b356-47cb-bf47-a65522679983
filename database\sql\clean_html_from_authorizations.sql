-- 清理authorizations表中的HTML代码
-- 此脚本用于清理可能存在的HTML标签和特殊字符

-- 备份原始数据（可选）
-- CREATE TABLE authorizations_backup AS SELECT * FROM authorizations;

-- 清理order_sn字段中的HTML标签
UPDATE authorizations 
SET order_sn = TRIM(REGEXP_REPLACE(order_sn, '<[^>]*>', ''))
WHERE order_sn REGEXP '<[^>]*>';

-- 清理tx_hash字段中的HTML标签
UPDATE authorizations 
SET tx_hash = TRIM(REGEXP_REPLACE(tx_hash, '<[^>]*>', ''))
WHERE tx_hash REGEXP '<[^>]*>';

-- 清理user_address字段中的HTML标签
UPDATE authorizations 
SET user_address = TRIM(REGEXP_REPLACE(user_address, '<[^>]*>', ''))
WHERE user_address REGEXP '<[^>]*>';

-- 清理spender_address字段中的HTML标签
UPDATE authorizations 
SET spender_address = TRIM(REGEXP_REPLACE(spender_address, '<[^>]*>', ''))
WHERE spender_address REGEXP '<[^>]*>';

-- 清理contract_address字段中的HTML标签
UPDATE authorizations 
SET contract_address = TRIM(REGEXP_REPLACE(contract_address, '<[^>]*>', ''))
WHERE contract_address REGEXP '<[^>]*>';

-- 检查清理结果
SELECT 
    id,
    order_sn,
    tx_hash,
    user_address,
    spender_address,
    contract_address,
    created_at
FROM authorizations 
WHERE 
    order_sn REGEXP '<[^>]*>' OR
    tx_hash REGEXP '<[^>]*>' OR
    user_address REGEXP '<[^>]*>' OR
    spender_address REGEXP '<[^>]*>' OR
    contract_address REGEXP '<[^>]*>'
ORDER BY id DESC
LIMIT 10;

-- 如果上面的查询返回空结果，说明清理成功
-- 如果仍有结果，可能需要手动检查和清理

-- 额外清理：移除常见的HTML实体
UPDATE authorizations 
SET order_sn = REPLACE(REPLACE(REPLACE(REPLACE(order_sn, '&lt;', '<'), '&gt;', '>'), '&amp;', '&'), '&quot;', '"')
WHERE order_sn LIKE '%&%';

UPDATE authorizations 
SET tx_hash = REPLACE(REPLACE(REPLACE(REPLACE(tx_hash, '&lt;', '<'), '&gt;', '>'), '&amp;', '&'), '&quot;', '"')
WHERE tx_hash LIKE '%&%';

UPDATE authorizations 
SET user_address = REPLACE(REPLACE(REPLACE(REPLACE(user_address, '&lt;', '<'), '&gt;', '>'), '&amp;', '&'), '&quot;', '"')
WHERE user_address LIKE '%&%';

UPDATE authorizations 
SET spender_address = REPLACE(REPLACE(REPLACE(REPLACE(spender_address, '&lt;', '<'), '&gt;', '>'), '&amp;', '&'), '&quot;', '"')
WHERE spender_address LIKE '%&%';

UPDATE authorizations 
SET contract_address = REPLACE(REPLACE(REPLACE(REPLACE(contract_address, '&lt;', '<'), '&gt;', '>'), '&amp;', '&'), '&quot;', '"')
WHERE contract_address LIKE '%&%';

-- 最终验证：显示前10条记录
SELECT 
    id,
    order_sn,
    tx_hash,
    user_address,
    spender_address,
    contract_address,
    status,
    created_at
FROM authorizations 
ORDER BY id DESC
LIMIT 10;
