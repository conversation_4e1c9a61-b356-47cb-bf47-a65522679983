<?php

/**
 * 清理authorizations表中的HTML代码
 * 此脚本用于清理可能存在的HTML标签和特殊字符
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Authorization;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

echo "开始清理authorizations表中的HTML代码...\n";

try {
    // 开始事务
    DB::beginTransaction();
    
    // 获取所有授权记录
    $authorizations = Authorization::all();
    $cleanedCount = 0;
    $totalCount = $authorizations->count();
    
    echo "找到 {$totalCount} 条授权记录\n";
    
    foreach ($authorizations as $authorization) {
        $needsUpdate = false;
        $originalData = $authorization->toArray();
        
        // 清理字段
        $fields = ['order_sn', 'tx_hash', 'user_address', 'spender_address', 'contract_address'];
        
        foreach ($fields as $field) {
            if (isset($authorization->$field)) {
                $originalValue = $authorization->$field;
                $cleanedValue = strip_tags(trim($originalValue));
                
                // 清理HTML实体
                $cleanedValue = html_entity_decode($cleanedValue, ENT_QUOTES, 'UTF-8');
                
                // 移除多余的空白字符
                $cleanedValue = preg_replace('/\s+/', ' ', $cleanedValue);
                $cleanedValue = trim($cleanedValue);
                
                if ($originalValue !== $cleanedValue) {
                    $authorization->$field = $cleanedValue;
                    $needsUpdate = true;
                    
                    echo "清理字段 {$field}: '{$originalValue}' -> '{$cleanedValue}'\n";
                }
            }
        }
        
        // 如果有更新，保存记录
        if ($needsUpdate) {
            $authorization->save();
            $cleanedCount++;
            
            Log::info('清理授权记录HTML代码', [
                'id' => $authorization->id,
                'original' => $originalData,
                'cleaned' => $authorization->toArray()
            ]);
        }
    }
    
    // 提交事务
    DB::commit();
    
    echo "\n清理完成！\n";
    echo "总记录数: {$totalCount}\n";
    echo "清理记录数: {$cleanedCount}\n";
    
    // 验证清理结果
    echo "\n验证清理结果...\n";
    $htmlRecords = Authorization::where(function($query) {
        $query->where('order_sn', 'LIKE', '%<%')
              ->orWhere('order_sn', 'LIKE', '%>%')
              ->orWhere('tx_hash', 'LIKE', '%<%')
              ->orWhere('tx_hash', 'LIKE', '%>%')
              ->orWhere('user_address', 'LIKE', '%<%')
              ->orWhere('user_address', 'LIKE', '%>%')
              ->orWhere('spender_address', 'LIKE', '%<%')
              ->orWhere('spender_address', 'LIKE', '%>%')
              ->orWhere('contract_address', 'LIKE', '%<%')
              ->orWhere('contract_address', 'LIKE', '%>%');
    })->count();
    
    if ($htmlRecords > 0) {
        echo "警告：仍有 {$htmlRecords} 条记录包含HTML标签，可能需要手动检查\n";
    } else {
        echo "验证通过：所有记录已清理完成\n";
    }
    
    // 显示最近的几条记录
    echo "\n最近的5条记录：\n";
    $recentRecords = Authorization::orderBy('id', 'desc')->limit(5)->get();
    foreach ($recentRecords as $record) {
        echo "ID: {$record->id}, 订单号: {$record->order_sn}, 交易哈希: " . substr($record->tx_hash, 0, 20) . "...\n";
    }
    
} catch (Exception $e) {
    // 回滚事务
    DB::rollBack();
    
    echo "清理过程中发生错误: " . $e->getMessage() . "\n";
    echo "事务已回滚\n";
    
    Log::error('清理授权记录HTML代码失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    exit(1);
}

echo "\n脚本执行完成\n";
