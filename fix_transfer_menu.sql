-- 修复转账记录菜单显示问题
-- 请在数据库管理工具中执行此SQL脚本

-- 1. 检查当前菜单状态
SELECT '=== 当前菜单状态检查 ===' as info;

-- 查看是否存在监控系统菜单
SELECT 
    id,
    parent_id,
    title,
    uri,
    `show` as visible
FROM admin_menu 
WHERE title LIKE '%监控%' OR title LIKE '%转账%' OR uri LIKE '%transfer%'
ORDER BY parent_id, `order`;

-- 2. 创建监控系统主菜单（如果不存在）
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (0, 8, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

-- 获取监控系统菜单ID
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' AND parent_id = 0 LIMIT 1);

-- 3. 删除可能存在的重复转账记录菜单
DELETE FROM `admin_menu` WHERE `title` = '转账记录' AND `parent_id` = @monitoring_menu_id;

-- 4. 添加转账记录子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (@monitoring_menu_id, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW());

-- 5. 确保其他监控系统子菜单存在
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(@monitoring_menu_id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW());

-- 6. 验证修复结果
SELECT '=== 修复后的菜单结构 ===' as info;

SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('📁 ', title)
        ELSE CONCAT('  📄 ', title, ' (', uri, ')')
    END as menu_structure,
    id,
    `show` as visible
FROM admin_menu 
WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id
ORDER BY parent_id, `order`;

-- 7. 检查相关表是否存在
SELECT '=== 相关表检查 ===' as info;

SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '说明',
    TABLE_ROWS as '记录数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('transfer_records', 'authorized_addresses', 'authorizations');

-- 8. 添加转账记录相关权限（重要！）
INSERT IGNORE INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`) VALUES
('转账记录管理', 'transfer-records', '', 'transfer-records*', NOW(), NOW()),
('监控系统管理', 'monitoring-system', '', 'monitor-dashboard*,authorizations*,authorized-addresses*,transfer-records*', NOW(), NOW());

-- 9. 获取管理员角色ID（通常是1）
SET @admin_role_id = (SELECT id FROM admin_roles WHERE slug = 'administrator' OR name = 'Administrator' LIMIT 1);

-- 10. 如果没有找到管理员角色，使用ID为1的角色
SET @admin_role_id = IFNULL(@admin_role_id, 1);

-- 11. 绑定权限到管理员角色
INSERT IGNORE INTO `admin_role_permissions` (`role_id`, `permission_id`)
SELECT @admin_role_id, id FROM `admin_permissions` WHERE `slug` IN ('transfer-records', 'monitoring-system');

-- 12. 绑定菜单到管理员角色（如果启用了角色菜单绑定）
INSERT IGNORE INTO `admin_role_menu` (`role_id`, `menu_id`)
SELECT @admin_role_id, id FROM `admin_menu` WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id;

-- 13. 显示完成信息
SELECT '🎉 转账记录菜单修复完成！' as message;
SELECT '💡 请刷新后台页面查看"监控系统 → 转账记录"菜单' as next_step;

-- 14. 显示权限绑定结果
SELECT '=== 权限绑定结果 ===' as info;
SELECT
    r.name as '角色名称',
    p.name as '权限名称',
    p.slug as '权限标识'
FROM admin_role_permissions rp
JOIN admin_roles r ON rp.role_id = r.id
JOIN admin_permissions p ON rp.permission_id = p.id
WHERE p.slug IN ('transfer-records', 'monitoring-system');

-- 15. 如果仍然无法显示，请检查以下几点：
/*
故障排除指南：

1. 清除浏览器缓存：
   - 按 Ctrl+F5 强制刷新页面
   - 或者清除浏览器缓存后重新访问

2. 检查用户权限：
   - 确保当前登录用户有访问菜单的权限
   - 检查角色权限配置
   - 本脚本已自动为管理员角色添加相关权限

3. 检查路由配置：
   - 确保 app/Admin/routes.php 中包含 transfer-records 路由
   - 确保 TransferRecordController 存在

4. 检查数据库表：
   - 确保 transfer_records 表已创建
   - 运行迁移：php artisan migrate

5. 重启服务：
   - 重启Web服务器
   - 清除Laravel缓存：php artisan cache:clear

6. 检查错误日志：
   - 查看 storage/logs/laravel.log
   - 查看Web服务器错误日志

7. 权限系统配置：
   - 本系统启用了菜单权限绑定
   - 确保用户角色有相应的菜单访问权限
   - 如果问题仍然存在，可以临时禁用权限检查进行测试
*/
