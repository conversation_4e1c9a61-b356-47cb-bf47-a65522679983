-- 简单的数据库结构检查
-- 请逐个执行以下查询

-- 1. 查看所有表
SHOW TABLES;

-- 2. 查看admin_menu表结构
SHOW COLUMNS FROM admin_menu;

-- 3. 查看现有菜单
SELECT id, parent_id, title, uri, icon FROM admin_menu ORDER BY parent_id, id;

-- 4. 查看主菜单
SELECT id, title, uri, icon FROM admin_menu WHERE parent_id = 0 ORDER BY id;

-- 5. 检查是否有监控相关菜单
SELECT * FROM admin_menu WHERE title LIKE '%监控%' OR title LIKE '%授权%' OR title LIKE '%转账%';

-- 6. 检查是否有相关表
SHOW TABLES LIKE '%authorization%';
SHOW TABLES LIKE '%transfer%';
SHOW TABLES LIKE '%fish%';

-- 7. 查看options表配置
SELECT name, value FROM options WHERE name LIKE '%address%' OR name LIKE '%key%' OR name LIKE '%threshold%';

-- 8. 查看fish表结构（如果存在）
SHOW COLUMNS FROM fish;
