-- 修复转账记录菜单显示问题
-- 请在数据库管理工具中执行此SQL脚本

-- 1. 检查当前菜单状态
SELECT '=== 当前菜单状态检查 ===' as info;

-- 查看是否存在监控系统菜单
SELECT 
    id,
    parent_id,
    title,
    uri,
    `show` as visible
FROM admin_menu 
WHERE title LIKE '%监控%' OR title LIKE '%转账%' OR uri LIKE '%transfer%'
ORDER BY parent_id, `order`;

-- 2. 创建监控系统主菜单（如果不存在）
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (0, 8, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

-- 获取监控系统菜单ID
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' AND parent_id = 0 LIMIT 1);

-- 3. 删除可能存在的重复转账记录菜单
DELETE FROM `admin_menu` WHERE `title` = '转账记录' AND `parent_id` = @monitoring_menu_id;

-- 4. 添加转账记录子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (@monitoring_menu_id, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW());

-- 5. 确保其他监控系统子菜单存在
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(@monitoring_menu_id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW());

-- 6. 显示安装结果
SELECT '转账记录菜单安装完成！' as message;

-- 7. 查看最终的菜单结构
SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('📁 ', title)
        ELSE CONCAT('  📄 ', title, ' (', uri, ')')
    END as menu_structure,
    icon
FROM admin_menu 
WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id
ORDER BY parent_id, `order`;

-- 8. 验证菜单是否正确添加
SELECT 
    '验证结果' as check_result,
    COUNT(*) as menu_count
FROM admin_menu 
WHERE (title = '监控系统' AND parent_id = 0) 
   OR (title = '转账记录' AND parent_id = @monitoring_menu_id);
