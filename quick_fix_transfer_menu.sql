-- 快速修复转账记录菜单显示问题
-- 在数据库管理工具中执行此脚本

-- 1. 创建监控系统主菜单
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (0, 8, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

-- 2. 获取监控系统菜单ID
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' AND parent_id = 0 LIMIT 1);

-- 3. 添加转账记录菜单
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (@monitoring_menu_id, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW());

-- 4. 添加其他子菜单
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(@monitoring_menu_id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW());

-- 5. 添加权限
INSERT IGNORE INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`) VALUES
('转账记录管理', 'transfer-records', '', 'transfer-records*', NOW(), NOW());

-- 6. 绑定权限到管理员角色
SET @admin_role_id = IFNULL((SELECT id FROM admin_roles WHERE slug = 'administrator' OR name = 'Administrator' LIMIT 1), 1);

INSERT IGNORE INTO `admin_role_permissions` (`role_id`, `permission_id`) 
SELECT @admin_role_id, id FROM `admin_permissions` WHERE `slug` = 'transfer-records';

INSERT IGNORE INTO `admin_role_menu` (`role_id`, `menu_id`) 
SELECT @admin_role_id, id FROM `admin_menu` WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id;

-- 7. 验证结果
SELECT '✅ 修复完成！' as status;
SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('📁 ', title)
        ELSE CONCAT('  📄 ', title, ' (', uri, ')')
    END as menu_structure
FROM admin_menu 
WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id
ORDER BY parent_id, `order`;

-- 执行完成后请：
-- 1. 刷新后台页面（Ctrl+F5）
-- 2. 查看左侧菜单是否出现"监控系统 → 转账记录"
