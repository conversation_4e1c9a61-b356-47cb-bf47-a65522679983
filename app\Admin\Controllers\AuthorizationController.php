<?php

namespace App\Admin\Controllers;

use App\Models\Authorization;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AuthorizationController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Authorization::with(['order']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('order_sn', '订单号')->copyable();
            $grid->column('tx_hash', '交易哈希')->copyable()->limit(20);
            $grid->column('user_address', '用户地址')->copyable()->limit(15);
            $grid->column('spender_address', '授权地址')->copyable()->limit(15);
            $grid->column('amount', '授权金额')->display(function ($value) {
                return $value . ' USDT';
            });
            $grid->column('contract_address', '合约地址')->copyable()->limit(15);
            
            $grid->column('status', '状态')->using([
                0 => '待验证',
                1 => '已验证', 
                2 => '验证失败'
            ])->label([
                0 => 'warning',
                1 => 'success',
                2 => 'danger'
            ]);
            
            $grid->column('verified_at', '验证时间');
            $grid->column('created_at', '创建时间')->sortable();
            
            // 禁用创建按钮（授权记录只能通过API创建）
            $grid->disableCreateButton();
            
            // 禁用编辑和删除（保持数据完整性）
            $grid->disableActions();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_sn', '订单号');
                $filter->like('tx_hash', '交易哈希');
                $filter->like('user_address', '用户地址');
                $filter->equal('status', '状态')->select([
                    0 => '待验证',
                    1 => '已验证',
                    2 => '验证失败'
                ]);
                $filter->between('created_at', '创建时间')->datetime();
            });
            
            // 导出功能
            $grid->export();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, Authorization::with(['order']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('order_sn', '订单号');
            $show->field('tx_hash', '交易哈希');
            $show->field('user_address', '用户地址');
            $show->field('spender_address', '授权地址');
            $show->field('amount', '授权金额')->as(function ($value) {
                return $value . ' USDT';
            });
            $show->field('contract_address', '合约地址');
            
            $show->field('status', '状态')->using([
                0 => '待验证',
                1 => '已验证',
                2 => '验证失败'
            ]);
            
            $show->field('verified_at', '验证时间');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 关联订单信息
            if ($show->model()->order) {
                $show->divider();
                $show->field('order.title', '订单标题');
                $show->field('order.email', '订单邮箱');
                $show->field('order.actual_price', '订单金额')->as(function ($value) {
                    return $value . ' 元';
                });
                $show->field('order.status', '订单状态')->using([
                    1 => '待支付',
                    2 => '待处理',
                    3 => '处理中',
                    4 => '已完成',
                    5 => '失败',
                    6 => '异常',
                    -1 => '过期'
                ]);
            }
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        // 授权记录不允许手动创建或编辑
        return Form::make(Authorization::class, function (Form $form) {
            $form->display('id', 'ID');
            $form->display('order_sn', '订单号');
            $form->display('tx_hash', '交易哈希');
            $form->display('user_address', '用户地址');
            $form->display('spender_address', '授权地址');
            $form->display('amount', '授权金额');
            $form->display('contract_address', '合约地址');
            $form->display('status', '状态');
            $form->display('verified_at', '验证时间');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 禁用所有操作
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewCheck();
        });
    }
}
